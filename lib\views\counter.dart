import 'package:crud/controllers/counterController.dart';
import 'package:flutter/material.dart';
import 'package:get_x_master/get_x_master.dart';

class HomePage extends ReactiveGetView<CounterController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('GetX Master Counter')),
      body: Center(
        child: Text(
          'Count: ${controller.count}', // Automatically updates
          style: Theme.of(context).textTheme.headlineMedium,
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.increment,
        child: Icon(Icons.add),
      ),
    );
  }
}