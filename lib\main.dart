import 'package:flutter/material.dart';
import 'package:crud/views/counter.dart';
import 'package:crud/controllers/counterController.dart';
import 'package:get_x_master/get_x_master.dart';

void main() {
  // Register the controller
  Get.put(CounterController());
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.greenAccent),
      ),
      home: HomePage(),
    );
  }
}
